<template>
  <div class="header-controls">
    <div class="form-check">
      <input
        class="form-check-input"
        type="checkbox"
        :checked="isLiveRefreshEnabled"
        @change="(e: Event) => $emit('toggleLiveRefresh', (e.target as HTMLInputElement).checked)"
        id="liveRefreshCheckbox"
      />
      <label class="form-check-label" for="liveRefreshCheckbox"> Live refresh </label>
    </div>
    <button class="btn btn-link p-0 text-decoration-none" @click="$emit('toggleTranspose')">
      <span class="fas fa-exchange-alt me-2"></span>
      {{ isTransposed ? 'Sources \\ Clients' : 'Clients \\ Sources' }}
    </button>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  isLiveRefreshEnabled: boolean
  isTransposed: boolean
}>()

defineEmits<{
  (e: 'toggleLiveRefresh', value: boolean): void
  (e: 'toggleTranspose'): void
}>()
</script>

<style scoped>
.header-controls {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-check {
  margin: 0;
  display: flex;
  align-items: center;
}

.form-check-input {
  margin-right: 0.5rem;
  margin-top: 0;
  cursor: pointer;
}

.form-check-label {
  font-size: 1rem;
  font-weight: 400;
  user-select: none;
  color: var(--bs-body-color);
  line-height: 1;
  margin-bottom: 0;
  margin-left: 0.3rem;
  cursor: pointer;
}

.btn-link {
  color: inherit;
  width: 100%;
  text-align: left;
  font-weight: normal;
  display: block;
  position: relative;
  z-index: var(--z-index-client-source-status-matrix-controls-btn-link);
  line-height: 1;
}

.btn-link:hover {
  color: var(--bs-primary);
}
</style>
