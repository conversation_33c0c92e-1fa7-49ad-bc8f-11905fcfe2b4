<template>
  <StatusTooltip :sections="sections" />
</template>

<script setup lang="ts">
import type { StatusInfo } from '@/types/statuses'
import { computed } from 'vue'
import StatusTooltip from '@/components/statuses/StatusTooltip.vue'
import { formatDateTimeISO } from '@/libs/temporal'

const props = defineProps<{ statusInfo: StatusInfo }>()

type Row = { label: string; value: string | number | null }
type Section = Row[]

const sections = computed(() => {
  const { scrapeStatus, errorType, data } = props.statusInfo
  const sections: Section[] = [
    [
      { label: 'Scrape', value: scrapeStatus },
      { label: 'Error', value: errorType || null },
    ],
  ]

  if (data) {
    sections.push([
      { label: 'Organization', value: data.organizationName || null },
      { label: 'Source', value: data.source || null },
      { label: 'Created', value: formatDateTimeISO(data.createdAt) },
      { label: 'Updated', value: formatDateTimeISO(data.updatedAt) },
      { label: 'Failed Scrapes', value: data.consecutiveFailedScrapeCount },
      { label: 'Last Success', value: formatDateTimeISO(data.lastSuccessTimestamp) },
      { label: 'Last Fail', value: formatDateTimeISO(data.lastFailTimestamp) },
      { label: 'Fail Reason', value: data.lastFailReason || null },
      { label: 'Last User', value: data.userEmail || null },
    ])
  }

  return sections
})
</script>
