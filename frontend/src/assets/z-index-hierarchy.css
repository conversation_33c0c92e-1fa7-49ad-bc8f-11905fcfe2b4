/*!
 * z-index-hierarchy.css
 *
 * Centralized store of all z-index values used throughout the application.
 *
 * Why?
 *   – Ensures a consistent stacking order and prevents overlapping conflicts.
 *   – Provides a single source of truth for easy maintenance and review.
 *   – Eliminates "magic numbers" spread across components.
 *
 * How to use:
 *   In your styles or Vue SFCs, reference these variables only:
 *     z-index: var(--z-index-<component-or-purpose>);
 */

:root {
  --z-index-notification-center: 2100;
  --z-index-status-context-menu: 2000;
  --z-index-status-legend: 1500;
  --z-index-status-legend-close-button: 1101;
  --z-index-status-tooltip: 1100;
  --z-index-theme-navbar-top: 1020; /* hardcoded in static/theme/assets/css/theme.css */
  --z-index-status-background: 20;
  --z-index-client-source-status-matrix-controls-btn-link: 10;
  --z-index-client-source-status-matrix-header-div-span: 5;
  --z-index-client-source-status-matrix-header-div: 1;
  --z-index-status-cell-hover: 1;
}
