import type { ClientStatusResponse } from './types'
import { type StatusInfo } from '@/types/statuses'
import { ErrorType } from '@/types/errors'
import type { GridStatus } from '@/domain/grids/types'
import {
  ScrapeStatus,
  ReportStatus,
  IN_PROGRESS_STATES,
  STALE_THRESHOLD_MS,
} from '@/types/statuses'
import type { Source } from '@/domain/types'
import { PlainDateTime } from '@/libs/temporal'
import axios from 'axios'

export const api = axios.create({
  baseURL: '/',
  headers: {
    'Content-Type': 'application/json',
  },
})

export function mapClientStatuses(statuses: ClientStatusResponse[]): GridStatus<StatusInfo>[] {
  return statuses.map((status) => {
    const reportStatus = status.state === ScrapeStatus.FINISHED ? ReportStatus.NONE : undefined

    let errorType: ErrorType | undefined
    let statusState: ScrapeStatus = status.state as ScrapeStatus

    if (status.state === ScrapeStatus.FAILED && status.last_fail_reason) {
      errorType = status.last_fail_reason as ErrorType
    }

    if (
      IN_PROGRESS_STATES.includes(status.state) &&
      new Date(status.updated_at) < new Date(Date.now() - STALE_THRESHOLD_MS)
    ) {
      errorType = ErrorType.STALE
      statusState = ScrapeStatus.FAILED
    }

    const statusInfo: StatusInfo = {
      scrapeStatus: statusState,
      reportStatus,
      errorType,
      data: {
        source: status.source as Source,
        organizationId: status.organization_id,
        organizationName: status.organization_name,
        lastOperationId: status.last_operation_id,
        createdAt: parseISODateTime(status.created_at),
        updatedAt: parseISODateTime(status.updated_at),
        consecutiveFailedScrapeCount: status.consecutive_failed_scrape_count,
        lastSuccessTimestamp: parseISODateTime(status.last_success_timestamp),
        lastFailTimestamp: parseISODateTime(status.last_fail_timestamp),
        lastFailReason: status.last_fail_reason,
        userId: status.user_id,
        lastOrigin: status.last_origin,
        userEmail: status.user_email,
        version: status.version,
      },
    }

    return {
      client: status.organization_name,
      source: status.source as Source,
      status: statusInfo,
    }
  })
}

function parseISODateTime<T extends string | null>(
  dateTimeStr: T,
): T extends null ? null : PlainDateTime {
  if (!dateTimeStr) return null as T extends null ? null : PlainDateTime
  const removeTimeZone = (dateTimeStr: string) => dateTimeStr.replace('Z', '')
  const removeMilliseconds = (dateTimeStr: string) => dateTimeStr.replace(/\.\d+/, '')
  const localDateTime = removeMilliseconds(removeTimeZone(dateTimeStr))
  return PlainDateTime.from(localDateTime) as T extends null ? null : PlainDateTime
}
