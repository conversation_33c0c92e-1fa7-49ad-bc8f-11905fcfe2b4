import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mapClientStatuses } from './utils'
import { ScrapeStatus, ReportStatus } from '@/types/statuses'
import { ErrorType } from '@/types/errors'
import { PlainDateTime } from '@/libs/temporal'

describe('mapClientStatuses', () => {
  const mockDate = new Date('2024-03-20T10:00:00Z')

  const baseInput = {
    source: 'TEST_SOURCE',
    organization_id: 'org-1',
    organization_name: 'Test Org',
    last_operation_id: 'op-1',
    created_at: '2024-03-20T09:00:00Z',
    updated_at: '2024-03-20T09:00:00Z',
    consecutive_failed_scrape_count: 0,
    last_success_timestamp: '2024-03-20T09:00:00Z',
    last_fail_timestamp: null,
    last_fail_reason: null,
    user_id: 'user-1',
    last_origin: 'origin-1',
    user_email: '<EMAIL>',
    version: 1,
  }

  beforeEach(() => {
    vi.useFakeTimers()
    vi.setSystemTime(mockDate)
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should map finished status correctly', () => {
    const input = [
      {
        ...baseInput,
        state: ScrapeStatus.FINISHED,
      },
    ]

    const result = mapClientStatuses(input)

    expect(result).toHaveLength(1)
    expect(result[0]).toEqual({
      client: 'Test Org',
      source: 'TEST_SOURCE',
      status: {
        scrapeStatus: ScrapeStatus.FINISHED,
        reportStatus: ReportStatus.NONE,
        errorType: undefined,
        data: {
          source: 'TEST_SOURCE',
          organizationId: 'org-1',
          organizationName: 'Test Org',
          lastOperationId: 'op-1',
          createdAt: PlainDateTime.from('2024-03-20T09:00:00'),
          updatedAt: PlainDateTime.from('2024-03-20T09:00:00'),
          consecutiveFailedScrapeCount: 0,
          lastSuccessTimestamp: PlainDateTime.from('2024-03-20T09:00:00'),
          lastFailTimestamp: null,
          lastFailReason: null,
          userId: 'user-1',
          lastOrigin: 'origin-1',
          userEmail: '<EMAIL>',
          version: 1,
        },
      },
    })
  })

  it('should map failed status with error correctly', () => {
    const input = [
      {
        ...baseInput,
        state: ScrapeStatus.FAILED,
        last_fail_reason: ErrorType.UNEXPECTED_ERROR,
      },
    ]

    const result = mapClientStatuses(input)

    expect(result).toHaveLength(1)
    expect(result[0].status.scrapeStatus).toBe(ScrapeStatus.FAILED)
    expect(result[0].status.errorType).toBe(ErrorType.UNEXPECTED_ERROR)
  })

  it('should mark stale in-progress status as failed', () => {
    const exactlyOneHourAgo = new Date('2024-03-20T09:00:00Z') // exactly 1 hour before mockDate
    const oneHourAndOneSecondAgo = new Date('2024-03-20T08:59:59Z') // 1 hour and 1 second before mockDate

    const input = [
      {
        ...baseInput,
        state: ScrapeStatus.STARTED,
        updated_at: exactlyOneHourAgo.toISOString(),
        last_success_timestamp: null,
      },
      {
        ...baseInput,
        state: ScrapeStatus.STARTED,
        updated_at: oneHourAndOneSecondAgo.toISOString(),
        last_success_timestamp: null,
      },
    ]

    const result = mapClientStatuses(input)

    expect(result).toHaveLength(2)
    expect(result[0].status.scrapeStatus).toBe(ScrapeStatus.STARTED)
    expect(result[0].status.errorType).toBeUndefined()
    expect(result[1].status.scrapeStatus).toBe(ScrapeStatus.FAILED)
    expect(result[1].status.errorType).toBe(ErrorType.STALE)
  })
})
