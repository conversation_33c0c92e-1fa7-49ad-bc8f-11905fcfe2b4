import dataoffice.settings
import pytest

SCP_TEST_ACCOUNT_ORG_ID = "o-rsFPhw"
SCP_TEST_ACCOUNT_USER_ID = "u-mp09bS"


# settings are imported first and immediately overwritten with dev settings
# so that modules can still use settings in module scope that executes on import
# Why dev settings? Because integrations tests are run against real dev environment,
# with assumption that data for test_user_id and test_organization_id will not change.
# ruff: noqa: E402
dataoffice.settings._settings = dataoffice.settings.DevSettings(env="dev")  # type: ignore


@pytest.fixture
def test_organization_id():
    return SCP_TEST_ACCOUNT_ORG_ID


@pytest.fixture
def test_user_id():
    return SCP_TEST_ACCOUNT_USER_ID
