import dataoffice.settings

# settings are imported first and immediately overwritten with test settings
# so that modules can still use settings in module scope that executes on import
# ruff: noqa: E402
dataoffice.settings._settings = dataoffice.settings.TestSettings(env="test")  # type: ignore


import json
from datetime import datetime

import pytest
from dataoffice import auth, main
from dataoffice.services.clients_statuses_service import UsersCache, get_users_cache
from fastapi.testclient import TestClient


@pytest.fixture
def anonymous_client():
    main.app.dependency_overrides[get_users_cache] = lambda: UsersCache()

    yield TestClient(main.app)

    main.app.dependency_overrides = {}


class FakeAuthSession(auth.AuthSession):
    def is_active(self):
        return True

    def get_access_token(self):
        return "test-token"


auth_session = FakeAuthSession(
    public_id="test",
    user=auth.User(username="test", full_name="Test User"),
)


@pytest.fixture
def authed_client(anonymous_client: TestClient):
    main.app.dependency_overrides[auth.active_session] = lambda: auth_session
    main.app.dependency_overrides[get_users_cache] = lambda: UsersCache()

    yield anonymous_client

    main.app.dependency_overrides = {}


@pytest.fixture
def date_time_encoder():
    class DateTimeEncoder(json.JSONEncoder):
        def default(self, o):
            if isinstance(o, datetime):
                return o.isoformat()
            return super().default(o)

    return DateTimeEncoder
