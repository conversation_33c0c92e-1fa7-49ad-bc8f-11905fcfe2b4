import json
from datetime import datetime

import pytest
from dataoffice.connectors.scrapers_service import (
    ScraperBinaryStatus,
    ScraperStatusProjection,
    block_scraper_status,
    get_organization_scraper_statuses,
    get_scraper_operation_history,
    get_scraper_statuses,
    unblock_scraper_status,
)
from pytest_mock import MockerFixture


@pytest.fixture
def scraper_status_projection():
    return ScraperStatusProjection(
        organization_id="123",
        source="microsoft_sales",
        state=ScraperBinaryStatus.SCHEDULED,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        consecutive_failed_scrape_count=0,
        last_success_timestamp=None,
        last_fail_timestamp=None,
        last_fail_reason=None,
        user_id="user123",
        last_origin=None,
        last_operation_id=None,
        version=1,
    )


@pytest.mark.asyncio
async def test_get_scraper_statuses(
    mocker: MockerFixture, scraper_status_projection, date_time_encoder
):
    mock_response = mocker.Mock()
    mock_response.status_code = 200
    mock_response.content = json.dumps(
        [scraper_status_projection.model_dump()], cls=date_time_encoder
    ).encode()

    mock_client = mocker.patch(
        "dataoffice.connectors.scrapers_service._client", autospec=True
    )
    mock_client.get = mocker.AsyncMock(return_value=mock_response)

    result = await get_scraper_statuses()

    mock_client.get.assert_called_once_with(
        "/scraper_state/all",
        params=None,
    )
    assert len(result) == 1
    assert result[0].organization_id == "123"
    assert result[0].source == "microsoft_sales"
    assert result[0].state == ScraperBinaryStatus.SCHEDULED


@pytest.mark.asyncio
async def test_get_organization_scraper_statuses(
    mocker: MockerFixture, scraper_status_projection, date_time_encoder
):
    organization_id = "123"
    mock_response = mocker.Mock()
    mock_response.status_code = 200
    mock_response.content = json.dumps(
        [scraper_status_projection.model_dump()], cls=date_time_encoder
    ).encode()

    mock_client = mocker.patch(
        "dataoffice.connectors.scrapers_service._client", autospec=True
    )
    mock_client.get = mocker.AsyncMock(return_value=mock_response)

    result = await get_organization_scraper_statuses(organization_id)
    assert len(result) == 1
    assert result[0].organization_id == organization_id


@pytest.mark.asyncio
async def test_block_scraper_status(mocker: MockerFixture):
    organization_id = "123"
    user_id = "user123"
    source = "microsoft_sales"
    mock_response = mocker.Mock()
    mock_response.status_code = 200

    mock_client = mocker.patch(
        "dataoffice.connectors.scrapers_service._client", autospec=True
    )
    mock_client.post = mocker.AsyncMock(return_value=mock_response)

    await block_scraper_status(organization_id, user_id, source)
    mock_client.post.assert_called_once()


@pytest.mark.asyncio
async def test_unblock_scraper_status(mocker: MockerFixture):
    organization_id = "123"
    user_id = "user123"
    source = "microsoft_sales"
    mock_response = mocker.Mock()
    mock_response.status_code = 200

    mock_client = mocker.patch(
        "dataoffice.connectors.scrapers_service._client", autospec=True
    )
    mock_client.post = mocker.AsyncMock(return_value=mock_response)

    await unblock_scraper_status(organization_id, user_id, source)
    mock_client.post.assert_called_once()


@pytest.mark.asyncio
async def test_get_scraper_operation_history(mocker: MockerFixture):
    organization_id = "123"
    source = "microsoft_sales"
    mock_response = mocker.Mock()
    mock_response.status_code = 200
    mock_response.content = json.dumps([]).encode()

    mock_client = mocker.patch(
        "dataoffice.connectors.scrapers_service._client", autospec=True
    )
    mock_client.get = mocker.AsyncMock(return_value=mock_response)

    result = await get_scraper_operation_history(organization_id, source)
    assert isinstance(result, list)
