import json
from datetime import datetime, timezone

import pytest
import respx
from dataoffice.connectors.scrapers_service import (
    DataTableResponse,
    ScraperBinaryStatus,
    ScraperStatusProjection,
)
from dataoffice.connectors.user_service import User
from dataoffice.services.clients_statuses_service import UsersCache
from dataoffice.settings import settings
from dataoffice.tabs.scraper_statuses.scraper_statuses import get_data
from httpx import Response
from starlette.requests import Request
from starlette.routing import Router
from starlette.staticfiles import StaticFiles


@pytest.fixture
def mock_request():
    request = Request(
        {
            "type": "http",
            "method": "GET",
            "headers": [(b"host", b"testserver")],
            "scheme": "http",
            "server": ("testserver", 80),
            "path": "/",
            "query_string": b"",
        }
    )
    router = Router()
    router.mount("/static", StaticFiles(directory="static"), name="static")
    request.scope["router"] = router
    return request


@pytest.fixture
def mock_auth_session():
    from dataoffice.auth import AuthSession

    return AuthSession(public_id="test-user")


@pytest.fixture
def scraper_status():
    return ScraperStatusProjection(
        organization_id="123",
        source="microsoft_sales",
        state=ScraperBinaryStatus.SCHEDULED,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
        consecutive_failed_scrape_count=0,
        last_success_timestamp=None,
        last_fail_timestamp=None,
        last_fail_reason=None,
        user_id="u-123",
        last_origin=None,
        last_operation_id=None,
        version=1,
    )


@pytest.fixture
def scraper_state_datatable_response(scraper_status):
    return DataTableResponse(
        data=[scraper_status],
        draw=1,
        length=1,
        recordsFiltered=1,
        recordsTotal=1,
        start=0,
    )


@pytest.fixture
def user():
    return User(
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        company_name="Test Company",
        id="u-123",
        legacy_id=1,
        verified=True,
        created_at=datetime.now(timezone.utc),
    )


@pytest.fixture
def users_cache():
    return UsersCache()


@pytest.mark.asyncio
async def test_scraper_statuses_endpoint_fill_user_and_organization(
    mock_request,
    mock_auth_session,
    scraper_status,
    scraper_state_datatable_response,
    user,
    date_time_encoder,
    users_cache,
):
    with respx.mock:
        # Mock scraper status response
        respx.get(f"{settings().scraper_service_url}/scraper_state/datatable").mock(
            return_value=Response(
                200,
                content=json.dumps(
                    scraper_state_datatable_response.model_dump(), cls=date_time_encoder
                ),
            )
        )

        # Mock user search response with the user
        respx.get(f"{settings().user_service_url}/user/search").mock(
            return_value=Response(
                200,
                content=json.dumps(
                    {"data": [user.model_dump()], "count": 1}, cls=date_time_encoder
                ),
            )
        )

        # Mock successful user fetch
        respx.get(f"{settings().user_service_url}/user/{scraper_status.user_id}").mock(
            return_value=Response(
                200, content=json.dumps(user.model_dump(), cls=date_time_encoder)
            )
        )
        result = await get_data(mock_request, users_cache)

        # Verify the result
        assert "data" in result
        data = result["data"]
        assert len(data) == 1
        assert data[0]["organization_id"] == "123"
        assert data[0]["source"] == "microsoft_sales"
        assert data[0]["state"] == ScraperBinaryStatus.SCHEDULED
        assert data[0]["organization_name"] == "Test Company"
        assert data[0]["user_email"] == "<EMAIL>"


@pytest.mark.asyncio
async def test_scraper_statuses_endpoint_fill_user_and_organization_on_missing_user(
    mock_request,
    mock_auth_session,
    scraper_status,
    scraper_state_datatable_response,
    date_time_encoder,
    users_cache,
):
    with respx.mock:
        # Mock scraper status response
        respx.get(f"{settings().scraper_service_url}/scraper_state/datatable").mock(
            return_value=Response(
                200,
                content=json.dumps(
                    scraper_state_datatable_response.model_dump(), cls=date_time_encoder
                ),
            )
        )

        # Mock empty user search response
        respx.get(f"{settings().user_service_url}/user/search").mock(
            return_value=Response(200, content=json.dumps({"data": [], "count": 0}))
        )

        # Mock 404 for specific user
        respx.get(f"{settings().user_service_url}/user/{scraper_status.user_id}").mock(
            return_value=Response(404)
        )

        # Call the endpoint
        result = await get_data(mock_request, users_cache)

        # Verify the result
        assert "data" in result
        data = result["data"]
        assert len(data) == 1
        assert data[0]["organization_id"] == "123"
        assert data[0]["source"] == "microsoft_sales"
        assert data[0]["state"] == ScraperBinaryStatus.SCHEDULED
        assert data[0]["organization_name"] == "<DELETED>"
        assert data[0]["user_email"] == ""


#
