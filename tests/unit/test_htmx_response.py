from datetime import datetime, timedelta, timezone

import pytest
from dataoffice.connectors.scrapers_service import (
    ScraperBinaryStatus,
    ScraperStatusProjection,
)
from dataoffice.templating.htmx_response import display_scraper_status


@pytest.fixture
def scraper_status_projection():
    return ScraperStatusProjection(
        organization_id="o-sample",
        source="steam_sales",
        state=ScraperBinaryStatus.FINISHED,
        created_at=datetime(2021, 1, 1, 12, 20, 30, 12),
        updated_at=datetime.now(timezone.utc),
        consecutive_failed_scrape_count=0,
        last_success_timestamp=None,
        last_fail_timestamp=None,
        last_fail_reason=None,
        user_id="u-sample",
        last_origin=None,
        last_operation_id=None,
        organization_name="Sample Organization",
        user_email="<EMAIL>",
        version=1,
    )


def test_display_scraper_status_finished(
    scraper_status_projection: ScraperStatusProjection,
):
    scraper_status_projection.state = ScraperBinaryStatus.FINISHED
    scraper_status_projection.updated_at = datetime.now(timezone.utc) - timedelta(
        minutes=30
    )
    result = display_scraper_status(scraper_status_projection)
    assert result == '<td class="green">FINISHED</td>'


def test_display_scraper_status_started_yellow_when_runs_shorter_than_60_mins(
    scraper_status_projection: ScraperStatusProjection,
):
    scraper_status_projection.state = ScraperBinaryStatus.STARTED
    scraper_status_projection.updated_at = datetime.now(timezone.utc) - timedelta(
        minutes=30
    )
    result = display_scraper_status(scraper_status_projection)
    assert result == '<td class="light-blue">STARTED</td>'


def test_display_scraper_status_started_blocked_when_runs_longer_than_60_mins(
    scraper_status_projection: ScraperStatusProjection,
):
    scraper_status_projection.state = ScraperBinaryStatus.STARTED
    scraper_status_projection.updated_at = datetime.now(timezone.utc) - timedelta(
        minutes=90
    )
    result = display_scraper_status(scraper_status_projection)
    assert result == '<td class="red">STALE</td>'


def test_display_scraper_status_yellow_when_unconfigured_for_a_long_time(
    scraper_status_projection: ScraperStatusProjection,
):
    scraper_status_projection.state = ScraperBinaryStatus.UNCONFIGURED
    scraper_status_projection.updated_at = datetime.now(timezone.utc) - timedelta(
        minutes=3600
    )
    result = display_scraper_status(scraper_status_projection)
    assert result == '<td class="yellow">UNCONFIGURED</td>'


def test_display_scraper_status_failed_with_reason(
    scraper_status_projection: ScraperStatusProjection,
):
    scraper_status_projection.state = ScraperBinaryStatus.FAILED
    scraper_status_projection.updated_at = datetime.now(timezone.utc) - timedelta(
        minutes=30
    )
    scraper_status_projection.last_fail_reason = "Some error occurred"
    result = display_scraper_status(scraper_status_projection)
    assert result == '<td class="red">Some error occurred</td>'


def test_display_scraper_status_failed_without_reason(
    scraper_status_projection: ScraperStatusProjection,
):
    scraper_status_projection.state = ScraperBinaryStatus.FAILED
    scraper_status_projection.updated_at = datetime.now(timezone.utc) - timedelta(
        minutes=30
    )
    result = display_scraper_status(scraper_status_projection)
    assert result == '<td class="red">FAILED</td>'
