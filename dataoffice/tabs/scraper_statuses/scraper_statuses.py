from dataoffice.connectors import scrapers_service
from dataoffice.services.clients_statuses_service import (
    UsersCacheDependency,
    fill_clients_with_organization_name_dict,
    get_clients_statuses_service,
)
from dataoffice.tab import Tab, TabDependencies
from fastapi import Request
from fastapi.responses import HTMLResponse
from pydantic import BaseModel

tab = Tab(
    title="Statuses",  # Tab title (as shown in the sidebar)
    template="scraper_statuses/scraper_statuses.html.j2",  # Default template for the tab
    prefix="/scraper_statuses",  # URL prefix for the tab
    section="Scrapers",  # Section in the sidebar where the tab is shown
    icon="fas fa-star",
)


@tab.routes.get("/")
async def index(deps: TabDependencies, users_cache: UsersCacheDependency):
    data = await get_clients_statuses_service(users_cache)
    return tab.render(deps, {"data": data})


class Body(BaseModel):
    organization_id: str
    user_id: str
    source: str


@tab.routes.post("/block")
async def block(data: Body):
    await scrapers_service.block_scraper_status(
        organization_id=data.organization_id, user_id=data.user_id, source=data.source
    )
    return HTMLResponse("Blocked")


@tab.routes.post("/unblock")
async def unblock(data: Body):
    await scrapers_service.unblock_scraper_status(
        organization_id=data.organization_id, user_id=data.user_id, source=data.source
    )
    return HTMLResponse("Unblocked")


# just a proxy to the scrapers_service
@tab.routes.get("/data")
async def get_data(request: Request, users_cache: UsersCacheDependency):
    result = await scrapers_service.get_scraper_statuses_datatable(request.url.query)
    result["data"] = await fill_clients_with_organization_name_dict(
        result["data"], users_cache
    )
    return result
