from datetime import datetime

from dataoffice.connectors.scrapers_service import ScraperBinaryStatus
from pydantic import BaseModel


class ClientStatus(BaseModel):
    organization_id: str
    user_id: str
    source: str
    state: ScraperBinaryStatus
    created_at: datetime
    updated_at: datetime
    consecutive_failed_scrape_count: int
    last_success_timestamp: datetime | None
    last_fail_timestamp: datetime | None
    last_fail_reason: str | None
    last_origin: str | None
    last_operation_id: str | None
    organization_name: str
    user_email: str
    version: int
