import time
from typing import Annotated, Dict

import httpx
from dataoffice.api.utils import strip_managed_partner_suffix
from dataoffice.connectors import scrapers_service, user_service
from dataoffice.services.types import ClientStatus
from fastapi import Depends


class UsersCache:
    """Class to manage user cache."""

    CACHE_TTL = 24 * 60 * 60  # 24 hours in seconds

    def __init__(self):
        self.users = {}
        self.last_cache_update = 0

    @property
    def is_cache_valid(self):
        return time.time() - self.last_cache_update < self.CACHE_TTL and bool(
            self.users
        )


_users_cache = UsersCache()


def get_users_cache() -> UsersCache:
    return _users_cache


UsersCacheDependency = Annotated[UsersCache, Depends(get_users_cache)]


async def _get_user_data(
    user_id: str, users: Dict[str, user_service.User]
) -> user_service.User:
    """Get user data from cache or fetch it if not available."""
    user = users.get(user_id)
    if user:
        return user

    try:
        user = await user_service.get_user(user_id)
    except httpx.HTTPStatusError as e:
        if e.response is not None and e.response.status_code == 404:
            return user_service.User.get_deleted_user()
        else:
            raise e

    users[user_id] = user
    return user


async def _get_all_users(cache: UsersCache) -> Dict[str, user_service.User]:
    """Fetch all users and store them in a cache."""

    if cache.is_cache_valid:
        return cache.users

    # Fetch all users with pagination
    all_users = []
    offset = 0
    limit = 1000

    while True:
        users = await user_service.search_users("", offset=offset, limit=limit)
        if not users:
            break

        all_users.extend(users)
        if len(users) < limit:
            break

        offset += limit

    # Update cache only if we found some users
    if all_users:
        cache.users = {user.id: user for user in all_users}
        cache.last_cache_update = time.time()

    return cache.users


async def get_clients_statuses_service(
    users_cache: UsersCache, managed_partners_only: bool = False
) -> list[ClientStatus]:
    scraper_statuses: list[
        scrapers_service.ScraperStatusProjection
    ] = await scrapers_service.get_scraper_statuses()
    users = await _get_all_users(users_cache)

    result: list[ClientStatus] = []
    for scraper_status in scraper_statuses:
        if not scraper_status.user_id.startswith("u-"):
            continue

        user = await _get_user_data(scraper_status.user_id, users)
        org_name = user.company_name or ""
        org_id = scraper_status.organization_id

        if managed_partners_only and not is_managed_partner(org_name, org_id):
            continue

        result.append(
            ClientStatus(
                **{
                    **scraper_status.model_dump(),
                    "organization_name": strip_managed_partner_suffix(org_name),
                    "user_email": user.email or "",
                }
            )
        )

    return result


def is_managed_partner(org_name: str, org_id: str) -> bool:
    return "Managed" in org_name or org_id in ORG_IDS_OF_ADDITIONAL_MANAGED_CLIENTS


ORG_IDS_OF_ADDITIONAL_MANAGED_CLIENTS = [
    "o-AL6Tv7",  # SUPERHOT
    "o-LIYf78",  # Raw Fury
    "o-X1fjLv",  # Secret Mode
    "o-PAmMTX",  # Red Hook Studios
    "o-bQLihp",  # Relic Entertainment, Inc
]


async def fill_clients_with_organization_name_dict(
    scraper_statuses: list[dict], users_cache: UsersCache
) -> list[dict]:
    users = await _get_all_users(users_cache)
    result = []

    for status in scraper_statuses:
        if status["user_id"].startswith("u-"):
            user = await _get_user_data(status["user_id"], users)
            status.update(
                {
                    "organization_name": user.company_name or "",
                    "user_email": user.email or "",
                }
            )
        result.append(status)

    return result
