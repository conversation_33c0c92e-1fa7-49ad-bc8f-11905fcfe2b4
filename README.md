<!--- docs
# Metadata used by our doc generator
title: Dataoffice
group: tools
-->

# Dataoffice

DPT's backoffice!

## Login to GitLab Container Registry

```bash
az login
az acr login --name crindie<PERSON><PERSON>in
```

When asked chose the `internal-cicd` subscription to have proper access to crindiebimain.
Use `app-dev` or `app-prod` for access to DLS or Machine Learning.

## Local setup

1. Install dependencies using poetry:

   ```bash
   $ poetry install
   ```

2. Prepare environment configuration

   Copy the example .env file:

      ```bash
      cp .env.example .env
      ```

   Open `.env` and set the required values. This file contains variables common to all environments (like authentication keys) and the `env` variable, which selects the environment:

      ```env
      env=dev
      ```

      The value of `env` determines which secondary file will be loaded for environment-specific variables:
      - `env=dev`   → `.env.dev`
      - `env=prod`  → `.env.prod`
      - `env=local` → `.env.local`

   Copy the example file for your chosen environment. For example, for `dev`:

      ```bash
      cp .env.dev.example .env.dev
      ```

      Then fill in the required secrets and service keys in `.env.dev` (see the example file for guidance).


   For details on production or local setup, see [Setup for prod and local](#setup-for-other-environments-local-prod).

3. Build frontend

   ```bash
   cd frontend/
   npm run build
   ```

4. Run locally using

   ```bash
   # (in poetry shell)
   $ poe start
   ```

   > <span style="color: red">**_IMPORTANT:_**</span> Be sure to access Dataoffice using http://localhost:8666, not http://127.0.0.1:8666, otherwise authentication will not work.

## Running tests

Not much is done in terms of testing, but feel free to add more.
Test using

```bash
# (in poetry shell)
$ poe test
```

## Adding a new tab

1. Copy the package `dataoffice/tabs/sample_tab` to `dataoffice/tabs/<your_tab>`.
1. Edit the file names inside the package to be consistent.
1. Edit the tab definition in your_tab.py, following the comments with instructions.
1. In `datoffice/main.py`, import the tab module (`from dataoffice.tabs.your_tab import your_tab`) and add it to `_sidebar_config`.
1. Read comments in `sample_tab.py` for more info.

## Adding a new item to the sidebar menu

Sidebar is populated in `datoffice/main.py`. You can add tabs or custom items, for example an external link.

## Frontend development

Dataoffice uses Bootstrap 5 with a custom premium theme. More details about the theme in [docs/theme.md](docs/theme.md).

Jinja2 is used as a primary templating engine and should be used for dynamic content where possible. If you need JS features, HTMX is available on or tabs or you can inline another framework in a tab (see `dataoffice/tabs/data_health/data_health.html.j2` as an example, it loads Vue).

If you need CSS, think first if you cannot use Bootstrap 5 tools to achieve what you want. Boostrap has tools for colors, margins, flexbox, alignment. Also, the theme has a lot of premade widgets and views that you can shamelessly copy in (see [docs/theme.md](docs/theme.md)). If you really need to add custom css, add it in `static/dataoffice.css`, but you're probably doing something wrong.

## Async

Be mindful of how FastAPI handles async vs non-async endpoints. Prefer async calls when possible. When writing connectors to other APIs, prefer to use async
code (e.g. httpx.AsyncClient) instead of blocking, synchronous IO.

## Deployment

- Dataoffice is deployed as a typical service as part of Single-Click IndieBI. Docker images built by Gitlab CI with appropriate tags are deployed by Argo.
- Use gitlab CI jobs to deploy to dev and prod.


## Setup for other environments (local, prod)

### Local

Assuming you have working `.env.dev` configuration, just copy:

```
cp .env.dev .env.local
```

With that you can run dataoffice against your local instance of S2, because `LocalSettings` sets `scraper_service_key` and `scraper_service_url` with proper values for you.

Next you can simply run:

```
env=local poe start
```

If you want more permanent setup and not use prefix in front of `poe start`, just set `env=local` in your `.env` file.

### Production

Set `env=prod` in `.env` and create a `.env.prod` file with the required secrets (ask your team for access).

Running dataoffice against production services is not recommended, however sometimes necessary to tests some features, for example targeted towards managed partners.
